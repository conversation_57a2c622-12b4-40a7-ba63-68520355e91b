import asyncio
import aiohttp
import json
import logging
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class ConnectionPoolManager:
    """Manages persistent HTTP connections with optimized pooling"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.rag_session: Optional[aiohttp.ClientSession] = None
        self._lock = asyncio.Lock()
    
    async def get_session(self, session_type: str = 'default') -> aiohttp.ClientSession:
        """Get or create a persistent session with connection pooling"""
        async with self._lock:
            if session_type == 'rag':
                if self.rag_session is None or self.rag_session.closed:
                    self.rag_session = await self._create_rag_session()
                return self.rag_session
            else:
                if self.session is None or self.session.closed:
                    self.session = await self._create_default_session()
                return self.session
    
    async def _create_rag_session(self) -> aiohttp.ClientSession:
        """Create optimized session for RAG API calls"""
        connector = aiohttp.TCPConnector(
            limit=50,              # Total connection pool size
            limit_per_host=20,     # Max connections per host
            ttl_dns_cache=300,     # DNS cache for 5 minutes
            use_dns_cache=True,    # Enable DNS caching
            keepalive_timeout=30,  # Keep connections alive for 30s
            enable_cleanup_closed=True,  # Clean up closed connections
            force_close=False,     # Reuse connections
            ssl=False if 'localhost' in str(self) else None  # Optimize for dev/prod
        )
        
        timeout = aiohttp.ClientTimeout(
            total=3.0,      # Reduced from 7.0 for voice applications
            connect=1.0,    # Connection timeout
            sock_read=2.0   # Socket read timeout
        )
        
        return aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'Connection': 'keep-alive',
                'User-Agent': 'TwilioVoiceBot/1.0'
            },
            raise_for_status=True  # Automatically raise for 4xx/5xx
        )
    
    async def _create_default_session(self) -> aiohttp.ClientSession:
        """Create session for other API calls"""
        connector = aiohttp.TCPConnector(
            limit=30,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=5.0)
        
        return aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'Content-Type': 'application/json'},
            raise_for_status=True
        )
    
    async def close_all(self):
        """Close all sessions - call this on shutdown"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self.rag_session and not self.rag_session.closed:
            await self.rag_session.close()
        
        # Wait for connections to close
        await asyncio.sleep(0.1)

# Global connection pool manager
connection_pool = ConnectionPoolManager()

class OptimizedFunctionHandler:
    """Optimized function call handler with connection pooling"""
    
    def __init__(self, base_url: str, org_id: str, pool=None):
        self.base_url = base_url
        self.org_id = org_id
        self.pool = pool
        self.rag_url = 'https://devquicktalk.najoomi.ai/api/chat'
        
        # Optimized RAG payload template
        self.rag_payload_template = {
            "organisation_id": "nayatel",
            "history": [],
            "user_language": "en",
            "lead_gen": False,
            "voicemessage": False,
            "provideindexname": "",
            "providenamespace": "",
            "agent_module_subscription": False,
            "user_data": [],
            "user_language_voice": None,
            "newPrompt": ""
        }
    
    async def handle_rag_call(self, user_query: str) -> Optional[str]:
        """Optimized RAG call with connection pooling"""
        try:
            # Get persistent session
            session = await connection_pool.get_session('rag')
            
            # Create minimal payload
            payload = self.rag_payload_template.copy()
            payload["question"] = user_query
            
            logger.info(f"RAG call for query: {user_query}")
            
            # Make request using persistent session
            async with session.post(self.rag_url, json=payload) as response:
                result_json = await response.json()
                result = result_json.get('text', '')
                logger.info(f"RAG response received: {len(result)} chars")
                return result
                
        except asyncio.TimeoutError:
            logger.error(f"RAG call timed out for query: {user_query}")
            return "I'm sorry, I'm having trouble accessing that information right now. Please try again."
        except aiohttp.ClientError as exc:
            logger.error(f"RAG call failed: {exc}")
            return "I'm experiencing technical difficulties. Please try again shortly."
        except Exception as exc:
            logger.error(f"Unexpected error in RAG call: {exc}")
            return "I apologize, but I'm unable to process that request right now."
    
    async def handle_external_api_call(self, function_name: str, arguments: Dict[str, Any]) -> Optional[Dict]:
        """Optimized external API call with connection pooling"""
        try:
            session = await connection_pool.get_session('default')
            api_url = f'{self.base_url}/{function_name}/{self.org_id}'
            
            logger.info(f"External API call: {function_name}")
            
            async with session.post(api_url, json=arguments) as response:
                result = await response.json()
                logger.info(f"External API response received")
                return result
                
        except asyncio.TimeoutError:
            logger.error(f"External API call timed out: {function_name}")
            return {"error": "Request timed out"}
        except aiohttp.ClientError as exc:
            logger.error(f"External API call failed: {function_name} - {exc}")
            return {"error": "API call failed"}
        except Exception as exc:
            logger.error(f"Unexpected error in external API call: {exc}")
            return {"error": "Unexpected error occurred"}

# Your updated main function handling logic
async def handle_function_calls_optimized(response, openai_ws, base_url, org_id, pool, dtmfs):
    """Your original function with optimized connection handling"""
    
    handler = OptimizedFunctionHandler(base_url, org_id, pool)
    
    if response.get('type') == 'response.done':
        for item in response['response']['output']:
            if item['type'] == 'function_call':
                arguments = json.loads(item['arguments'])
                call_id = item["call_id"]
                function_name = item['name']
                
                result = None
                logger.info(f"Function invoked: {function_name}, Arguments: {arguments}")
                
                if function_name == 'handle_outof_scope_cases':
                    # Use optimized RAG call
                    result = await handler.handle_rag_call(arguments.get('user_query'))
                    
                elif function_name == 'get_order_status' and org_id == 'TCS':
                    result = await get_order_status_async(arguments, pool)
                    
                elif function_name == 'register_complaint' and org_id == 'TCS':
                    result = await store_customer_complaint(arguments, pool)
                    
                elif function_name == 'complaint_status' and org_id == 'TCS':
                    result = await complaint_status(arguments, pool)
                    
                else:
                    # Use optimized external API call
                    result = await handler.handle_external_api_call(function_name, arguments)
                
                # Handle DTMF cleanup
                if dtmfs:
                    delete_dtmf_message = {
                        "type": "conversation.item.delete",
                        "item_id": "msg_001"
                    }
                    await openai_ws.send(json.dumps(delete_dtmf_message))
                    dtmfs = None
                    logger.info('Deleted DTMF in conversation context')
                
                # Send function result back
                function_output = json.dumps(result)
                response_message = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "function_call_output",
                        "output": function_output,
                        "call_id": call_id
                    }
                }
                await openai_ws.send(json.dumps(response_message))
                await openai_ws.send(json.dumps({"type": "response.create"}))

# Application lifecycle management
@asynccontextmanager
async def lifespan_manager():
    """Context manager for application lifecycle"""
    try:
        # Initialize connection pool on startup
        logger.info("Initializing connection pool...")
        yield
    finally:
        # Close all connections on shutdown
        logger.info("Closing connection pool...")
        await connection_pool.close_all()

# Usage in your main application
async def main():
    async with lifespan_manager():
        # Your main application logic here
        pass

# For testing connection pool performance
async def test_performance():
    """Test to demonstrate performance improvement"""
    import time
    
    # Test without connection pool (your current method)
    start_time = time.time()
    for i in range(10):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://httpbin.org/delay/0.1') as response:
                await response.text()
    without_pool_time = time.time() - start_time
    
    # Test with connection pool
    start_time = time.time()
    session = await connection_pool.get_session('default')
    for i in range(10):
        async with session.get('https://httpbin.org/delay/0.1') as response:
            await response.text()
    with_pool_time = time.time() - start_time
    
    print(f"Without pool: {without_pool_time:.2f}s")
    print(f"With pool: {with_pool_time:.2f}s")
    print(f"Improvement: {((without_pool_time - with_pool_time) / without_pool_time * 100):.1f}%")

if __name__ == "__main__":
    asyncio.run(test_performance())